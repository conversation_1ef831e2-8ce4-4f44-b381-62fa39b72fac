package com.finddreams.dreamnotes.richtext

import com.finddreams.dreamnotes.richtext.RichTextUtils
import org.junit.Test
import org.junit.Assert.*

/**
 * 富文本工具函数的单元测试
 */
class RichTextUtilsTest {

    @Test
    fun testCreateSimpleHtml() {
        val text = "Hello World"
        val html = RichTextUtils.createSimpleHtml(text)

        assertNotNull(html)
        assertTrue(html.contains("Hello World"))
        assertTrue(html.contains("<html>"))
        assertTrue(html.contains("<body>"))
    }

    @Test
    fun testExtractPlainText() {
        val html = "<html><body><b>Bold</b> <i>Italic</i> text</body></html>"
        val plainText = RichTextUtils.extractPlainText(html)

        assertNotNull(plainText)
        assertEquals("Bold Italic text", plainText)
    }

    @Test
    fun testContainsTodoItems() {
        val htmlWithTodos = """
            <html>
            <body>
                <checkbox checked="false" index="0"/>未完成任务
                <br>
                <checkbox checked="true" index="1"/>已完成任务
            </body>
            </html>
        """.trimIndent()

        val htmlWithoutTodos = "<html><body>普通文本</body></html>"

        assertTrue(RichTextUtils.containsTodoItems(htmlWithTodos))
        assertFalse(RichTextUtils.containsTodoItems(htmlWithoutTodos))
    }

    @Test
    fun testCountTodoItems() {
        val html = """
            <html>
            <body>
                <checkbox checked="false" index="0"/>未完成任务1
                <br>
                <checkbox checked="true" index="1"/>已完成任务1
                <br>
                <checkbox checked="true" index="2"/>已完成任务2
                <br>
                <checkbox checked="false" index="3"/>未完成任务2
            </body>
            </html>
        """.trimIndent()

        val (completed, total) = RichTextUtils.countTodoItems(html)

        assertEquals(2, completed)
        assertEquals(4, total)
    }

    @Test
    fun testCreateTodoExample() {
        val html = RichTextUtils.createTodoExample()

        assertNotNull(html)
        assertTrue(html.contains("<html>"))
        assertTrue(html.contains("<checkbox"))
        assertTrue(html.contains("checked=\"true\""))
        assertTrue(html.contains("checked=\"false\""))
        assertTrue(html.contains("indent="))
    }

    @Test
    fun testExtractPlainTextWithSpecialChars() {
        val html = "<html><body>&lt;test&gt; &amp; &nbsp; content</body></html>"
        val plainText = RichTextUtils.extractPlainText(html)

        assertEquals("<test> &   content", plainText)
    }

    @Test
    fun testEmptyHtml() {
        val html = "<html><body></body></html>"
        val plainText = RichTextUtils.extractPlainText(html)

        assertEquals("", plainText)
    }

    @Test
    fun testCountTodoItemsEmpty() {
        val html = "<html><body>没有待办事项</body></html>"
        val (completed, total) = RichTextUtils.countTodoItems(html)

        assertEquals(0, completed)
        assertEquals(0, total)
    }
}
