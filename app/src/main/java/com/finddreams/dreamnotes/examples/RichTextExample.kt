package com.finddreams.dreamnotes.examples

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.finddreams.dreamnotes.richtext.RichTextEditor
import com.finddreams.dreamnotes.richtext.RichTextViewer
import com.finddreams.dreamnotes.richtext.RichTextUtils
import com.finddreams.dreamnotes.ui.theme.DreamNotesTheme

/**
 * 富文本编辑器使用示例
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RichTextExample() {
    var htmlContent by remember { mutableStateOf(RichTextUtils.createTodoExample()) }
    var showPreview by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // 顶部工具栏
        TopAppBar(
            title = { Text("富文本编辑器示例") },
            actions = {
                TextButton(
                    onClick = { showPreview = !showPreview }
                ) {
                    Text(if (showPreview) "编辑" else "预览")
                }
            }
        )
        
        if (showPreview) {
            // 预览模式
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "预览模式",
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    RichTextViewer(
                        html = htmlContent,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            // 显示HTML源码
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "HTML源码",
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Text(
                        text = htmlContent,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp)
                    )
                }
            }
            
            // 统计信息
            val (completed, total) = RichTextUtils.countTodoItems(htmlContent)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "统计信息",
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Text("待办事项: $completed/$total 已完成")
                    Text("纯文本长度: ${RichTextUtils.extractPlainText(htmlContent).length} 字符")
                    Text("包含待办事项: ${if (RichTextUtils.containsTodoItems(htmlContent)) "是" else "否"}")
                }
            }
        } else {
            // 编辑模式
            RichTextEditor(
                initialHtml = htmlContent,
                onContentChanged = { newHtml ->
                    htmlContent = newHtml
                },
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * 简单的富文本编辑器示例
 */
@Composable
fun SimpleRichTextExample() {
    var content by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        Text(
            text = "简单富文本编辑器",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(16.dp)
        )
        
        RichTextEditor(
            initialHtml = content,
            onContentChanged = { newContent ->
                content = newContent
            },
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 只读富文本查看器示例
 */
@Composable
fun RichTextViewerExample() {
    val sampleHtml = """
        <html>
        <body>
            <h1>示例文档</h1>
            <p>这是一个<b>富文本</b>查看器的示例。</p>
            <br>
            <p>支持的功能包括：</p>
            <ul>
                <li><b>加粗文本</b></li>
                <li><i>斜体文本</i></li>
                <li><u>下划线文本</u></li>
                <li><strike>删除线文本</strike></li>
                <li><span style="color:#ff0000">彩色文本</span></li>
            </ul>
            <br>
            <p>待办事项：</p>
            <checkbox checked="true" index="0"/>已完成的任务
            <br>
            <checkbox checked="false" index="1"/>未完成的任务
            <br>
            <div indent="1">
                <checkbox checked="false" index="2"/>缩进的子任务
            </div>
        </body>
        </html>
    """.trimIndent()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "富文本查看器示例",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        RichTextViewer(
            html = sampleHtml,
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RichTextExamplePreview() {
    DreamNotesTheme {
        RichTextExample()
    }
}

@Preview(showBackground = true)
@Composable
fun SimpleRichTextExamplePreview() {
    DreamNotesTheme {
        SimpleRichTextExample()
    }
}

@Preview(showBackground = true)
@Composable
fun RichTextViewerExamplePreview() {
    DreamNotesTheme {
        RichTextViewerExample()
    }
}
