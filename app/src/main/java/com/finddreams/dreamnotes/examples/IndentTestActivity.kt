package com.finddreams.dreamnotes.examples

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.finddreams.dreamnotes.richtext.RichTextEditor
import com.finddreams.dreamnotes.richtext.RichTextUtils
import com.finddreams.dreamnotes.ui.theme.DreamNotesTheme

/**
 * 缩进功能测试Activity
 */
class IndentTestActivity : ComponentActivity() {
    
    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            DreamNotesTheme {
                Scaffold(
                    topBar = {
                        TopAppBar(
                            title = { Text("缩进功能测试") }
                        )
                    }
                ) { innerPadding ->
                    IndentTestScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun IndentTestScreen(modifier: Modifier = Modifier) {
    var htmlContent by remember { mutableStateOf(createIndentTestContent()) }
    var showInfo by remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // 信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "缩进功能测试说明",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "• 点击增加缩进按钮测试多级缩进\n" +
                            "• 观察文本是否自动换行\n" +
                            "• 验证文本不会被挤出屏幕\n" +
                            "• 测试最大缩进限制\n" +
                            "• 查看缩进指示线效果",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = { showInfo = !showInfo }
                ) {
                    Text(if (showInfo) "隐藏详情" else "显示详情")
                }
                
                if (showInfo) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "技术实现：\n" +
                                "• 最大缩进限制为屏幕宽度的60%\n" +
                                "• 基础缩进大小：48dp\n" +
                                "• 自动换行：setHorizontallyScrolling(false)\n" +
                                "• 虚线指示器：渐变透明度效果\n" +
                                "• 动态屏幕宽度检测",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        // 富文本编辑器
        RichTextEditor(
            initialHtml = htmlContent,
            onContentChanged = { newHtml ->
                htmlContent = newHtml
            },
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 创建缩进测试内容
 */
private fun createIndentTestContent(): String {
    return """
        <html>
        <body>
            <b>缩进功能测试文档</b>
            <br><br>
            
            <div>1. 这是第一级内容，没有缩进。请尝试选中这行文字，然后点击增加缩进按钮。</div>
            <br>
            
            <div indent="1">2. 这是第二级内容，有一级缩进。这行文字比较长，用来测试在缩进情况下的自动换行效果。请观察文字是否会自动换行而不是被挤出屏幕。</div>
            <br>
            
            <div indent="2">3. 这是第三级内容，有二级缩进。继续测试更深层级的缩进效果。这里的文字也比较长，用来验证多级缩进下的文本显示效果。</div>
            <br>
            
            <div indent="3">4. 这是第四级内容，有三级缩进。在这个级别，缩进已经比较深了，但文字仍然应该完全可见。</div>
            <br>
            
            <div>5. 回到无缩进级别。请尝试以下操作：</div>
            <div indent="1">• 选中不同行的文字</div>
            <div indent="1">• 点击增加缩进按钮</div>
            <div indent="1">• 观察缩进指示线</div>
            <div indent="1">• 测试最大缩进限制</div>
            <br>
            
            <div>6. 待办事项缩进测试：</div>
            <checkbox checked="false" index="0"/>普通待办事项
            <br>
            <div indent="1">
                <checkbox checked="false" index="1"/>一级缩进的待办事项，这个文字比较长，用来测试缩进状态下的待办事项换行效果
            </div>
            <br>
            <div indent="2">
                <checkbox checked="true" index="2"/>二级缩进的已完成待办事项
            </div>
            <br>
            
            <div>7. 混合格式测试：</div>
            <div indent="1">
                <b>加粗文字</b> + <i>斜体文字</i> + <u>下划线文字</u> + <span style="color:#ff0000">红色文字</span>
            </div>
            <br>
            
            <div>8. 长文本换行测试：</div>
            <div indent="1">
                这是一段很长很长的文字，专门用来测试在缩进状态下的自动换行功能。这段文字应该会自动换行到下一行，而不会被挤出屏幕边界。无论缩进级别有多深，用户都应该能够看到完整的文字内容，不需要水平滚动。
            </div>
            <br>
            
            <div indent="2">
                这是另一段长文字，在二级缩进下测试。文字内容包含各种标点符号：，。！？；：""''（）【】《》等等。这些都应该正确显示和换行。
            </div>
        </body>
        </html>
    """.trimIndent()
}

/**
 * 预览组件
 */
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
fun IndentTestScreenPreview() {
    DreamNotesTheme {
        IndentTestScreen()
    }
}
