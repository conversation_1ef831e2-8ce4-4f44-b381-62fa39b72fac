package com.finddreams.dreamnotes

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.finddreams.dreamnotes.richtext.RichTextEditor
import com.finddreams.dreamnotes.richtext.RichTextUtils
import com.finddreams.dreamnotes.ui.theme.DreamNotesTheme

class MainActivity : ComponentActivity() {
    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            DreamNotesTheme {
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        TopAppBar(
                            title = { Text("DreamNotes 富文本编辑器") }
                        )
                    }
                ) { innerPadding ->
                    RichTextEditorDemo(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun RichTextEditorDemo(modifier: Modifier = Modifier) {
    var htmlContent by remember { mutableStateOf(RichTextUtils.createTodoExample()) }

    RichTextEditor(
        initialHtml = htmlContent,
        onContentChanged = { newHtml ->
            htmlContent = newHtml
        },
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun RichTextEditorPreview() {
    DreamNotesTheme {
        RichTextEditorDemo()
    }
}