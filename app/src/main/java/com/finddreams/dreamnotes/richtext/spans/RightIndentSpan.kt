package com.finddreams.dreamnotes.richtext.spans

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.text.Layout
import android.text.style.LeadingMarginSpan
import kotlin.math.max

/**
 * 右缩进Span，实现向右缩进到屏幕最右边时自动换行到下一行最右边的效果
 */
class RightIndentSpan(
    private val indentLevel: Int,
    private val baseIndentSize: Int = 48,
    private val minTextWidth: Int = 120 // 最小文本宽度，确保文本可读性
) : LeadingMarginSpan {
    
    companion object {
        private var screenWidth: Int = 0
        
        fun setScreenWidth(width: Int) {
            screenWidth = width
        }
        
        private fun getScreenWidth(): Int {
            if (screenWidth == 0) {
                screenWidth = Resources.getSystem().displayMetrics.widthPixels
            }
            return screenWidth
        }
    }
    
    override fun getLeadingMargin(first: Boolean): Int {
        val screenWidth = getScreenWidth()
        val totalIndent = indentLevel * baseIndentSize
        
        // 计算右缩进：从屏幕右边向左缩进
        val rightIndent = totalIndent
        
        // 确保至少保留最小文本宽度
        val maxAllowedIndent = screenWidth - minTextWidth
        val actualRightIndent = kotlin.math.min(rightIndent, maxAllowedIndent)
        
        // 返回左边距，使文本从右边开始显示
        return max(0, screenWidth - actualRightIndent - minTextWidth)
    }
    
    override fun drawLeadingMargin(
        canvas: Canvas?,
        paint: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: Layout?
    ) {
        // 绘制右缩进指示器
        if (indentLevel > 0 && canvas != null && paint != null) {
            val originalColor = paint.color
            val originalStrokeWidth = paint.strokeWidth
            
            val screenWidth = getScreenWidth()
            
            // 绘制右侧缩进指示线
            for (i in 1..indentLevel) {
                val lineX = screenWidth - (i * baseIndentSize)
                
                if (lineX > x) {
                    // 使用渐变透明度
                    val alpha = (255 * (1.0f - (i - 1) * 0.15f)).toInt().coerceIn(100, 255)
                    paint.color = (alpha shl 24) or 0x00FF6B6B // 使用红色系来区分右缩进
                    paint.strokeWidth = 2f
                    
                    // 绘制虚线效果
                    val dashLength = 6f
                    val gapLength = 3f
                    var currentY = top.toFloat()
                    
                    while (currentY < bottom) {
                        val endY = kotlin.math.min(currentY + dashLength, bottom.toFloat())
                        canvas.drawLine(lineX.toFloat(), currentY, lineX.toFloat(), endY, paint)
                        currentY += dashLength + gapLength
                    }
                }
            }
            
            // 恢复原始画笔设置
            paint.color = originalColor
            paint.strokeWidth = originalStrokeWidth
        }
    }
    
    /**
     * 获取缩进级别
     */
    fun getIndentLevel(): Int = indentLevel
    
    /**
     * 获取实际的右缩进大小（像素）
     */
    fun getActualRightIndentSize(): Int {
        val screenWidth = getScreenWidth()
        val totalIndent = indentLevel * baseIndentSize
        val maxAllowedIndent = screenWidth - minTextWidth
        return kotlin.math.min(totalIndent, maxAllowedIndent)
    }
    
    /**
     * 获取可用的文本宽度
     */
    fun getAvailableTextWidth(): Int {
        val screenWidth = getScreenWidth()
        val rightIndent = getActualRightIndentSize()
        val leftMargin = getLeadingMargin(true)
        return screenWidth - leftMargin - rightIndent
    }
    
    /**
     * 创建新的右缩进级别
     */
    fun withIndentLevel(newLevel: Int): RightIndentSpan {
        return RightIndentSpan(newLevel, baseIndentSize, minTextWidth)
    }
    
    /**
     * 检查是否达到最大右缩进限制
     */
    fun isAtMaxRightIndent(): Boolean {
        val screenWidth = getScreenWidth()
        val totalIndent = indentLevel * baseIndentSize
        val maxAllowedIndent = screenWidth - minTextWidth
        return totalIndent >= maxAllowedIndent
    }
    
    /**
     * 获取建议的最大右缩进级别
     */
    fun getMaxRecommendedLevel(): Int {
        val screenWidth = getScreenWidth()
        val maxAllowedIndent = screenWidth - minTextWidth
        return maxAllowedIndent / baseIndentSize
    }
    
    /**
     * 计算文本在当前缩进下的右边界
     */
    fun getTextRightBoundary(): Int {
        val screenWidth = getScreenWidth()
        val rightIndent = getActualRightIndentSize()
        return screenWidth - rightIndent
    }
}
