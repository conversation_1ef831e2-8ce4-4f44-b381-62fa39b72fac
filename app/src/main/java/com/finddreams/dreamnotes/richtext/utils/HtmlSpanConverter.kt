package com.finddreams.dreamnotes.richtext.utils

import android.graphics.Color
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.*
import com.finddreams.dreamnotes.richtext.spans.CheckboxSpan
import com.finddreams.dreamnotes.richtext.spans.IndentSpan
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode

/**
 * HTML与Span之间的转换工具类
 */
class HtmlSpanConverter {
    
    companion object {
        private const val CHECKBOX_PLACEHOLDER = "☐"
        private const val CHECKBOX_CHECKED_PLACEHOLDER = "☑"
    }
    
    /**
     * 将HTML转换为SpannableString
     */
    fun fromHtml(html: String, onCheckboxClick: ((Int, Boolean) -> Unit)? = null): SpannableStringBuilder {
        val document = Jsoup.parse(html)
        val spannable = SpannableStringBuilder()
        
        processNode(document.body(), spannable, onCheckboxClick)
        
        return spannable
    }
    
    /**
     * 将SpannableString转换为HTML
     */
    fun toHtml(spannable: Spannable): String {
        val html = StringBuilder()
        html.append("<html><body>")
        
        val text = spannable.toString()
        var currentPos = 0
        
        while (currentPos < text.length) {
            val lineEnd = text.indexOf('\n', currentPos).let { 
                if (it == -1) text.length else it 
            }
            
            val lineText = text.substring(currentPos, lineEnd)
            if (lineText.isNotEmpty()) {
                processLineToHtml(spannable, currentPos, lineEnd, html)
            }
            
            if (lineEnd < text.length) {
                html.append("<br>")
            }
            
            currentPos = lineEnd + 1
        }
        
        html.append("</body></html>")
        return html.toString()
    }
    
    private fun processNode(
        node: Node, 
        spannable: SpannableStringBuilder,
        onCheckboxClick: ((Int, Boolean) -> Unit)?
    ) {
        when (node) {
            is TextNode -> {
                spannable.append(node.text())
            }
            is Element -> {
                val startPos = spannable.length
                
                when (node.tagName().lowercase()) {
                    "checkbox" -> {
                        val isChecked = node.attr("checked") == "true"
                        val checkboxIndex = node.attr("index").toIntOrNull() ?: 0
                        
                        spannable.append(if (isChecked) CHECKBOX_CHECKED_PLACEHOLDER else CHECKBOX_PLACEHOLDER)
                        
                        val checkboxSpan = CheckboxSpan(isChecked) { newChecked ->
                            onCheckboxClick?.invoke(checkboxIndex, newChecked)
                        }
                        
                        spannable.setSpan(
                            checkboxSpan,
                            startPos,
                            spannable.length,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    "br" -> {
                        spannable.append("\n")
                    }
                    else -> {
                        // 处理子节点
                        for (child in node.childNodes()) {
                            processNode(child, spannable, onCheckboxClick)
                        }
                        
                        val endPos = spannable.length
                        if (startPos < endPos) {
                            applyElementStyle(node, spannable, startPos, endPos)
                        }
                    }
                }
            }
        }
    }
    
    private fun applyElementStyle(element: Element, spannable: SpannableStringBuilder, start: Int, end: Int) {
        when (element.tagName().lowercase()) {
            "b", "strong" -> {
                spannable.setSpan(
                    StyleSpan(Typeface.BOLD),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            "i", "em" -> {
                spannable.setSpan(
                    StyleSpan(Typeface.ITALIC),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            "u" -> {
                spannable.setSpan(
                    UnderlineSpan(),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            "strike", "s" -> {
                spannable.setSpan(
                    StrikethroughSpan(),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            "span" -> {
                val style = element.attr("style")
                parseInlineStyle(style, spannable, start, end)
            }
            "div" -> {
                val indentLevel = element.attr("indent").toIntOrNull() ?: 0
                if (indentLevel > 0) {
                    spannable.setSpan(
                        IndentSpan(indentLevel),
                        start, end,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }
    }
    
    private fun parseInlineStyle(style: String, spannable: SpannableStringBuilder, start: Int, end: Int) {
        val styles = style.split(";")
        
        for (styleRule in styles) {
            val parts = styleRule.split(":")
            if (parts.size == 2) {
                val property = parts[0].trim()
                val value = parts[1].trim()
                
                when (property) {
                    "color" -> {
                        try {
                            val color = Color.parseColor(value)
                            spannable.setSpan(
                                ForegroundColorSpan(color),
                                start, end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        } catch (e: Exception) {
                            // 忽略无效颜色
                        }
                    }
                    "font-size" -> {
                        val size = value.replace("px", "").toFloatOrNull()
                        if (size != null) {
                            spannable.setSpan(
                                AbsoluteSizeSpan(size.toInt(), false),
                                start, end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                }
            }
        }
    }
    
    private fun processLineToHtml(
        spannable: Spannable,
        start: Int,
        end: Int,
        html: StringBuilder
    ) {
        val spans = spannable.getSpans(start, end, Any::class.java)
        
        // 检查是否有缩进
        val indentSpan = spans.find { it is IndentSpan } as? IndentSpan
        if (indentSpan != null) {
            html.append("<div indent=\"${indentSpan.getIndentLevel()}\">")
        }
        
        var currentPos = start
        
        while (currentPos < end) {
            val char = spannable[currentPos]
            
            // 检查checkbox
            if (char == CHECKBOX_PLACEHOLDER[0] || char == CHECKBOX_CHECKED_PLACEHOLDER[0]) {
                val checkboxSpans = spannable.getSpans(currentPos, currentPos + 1, CheckboxSpan::class.java)
                if (checkboxSpans.isNotEmpty()) {
                    val isChecked = char == CHECKBOX_CHECKED_PLACEHOLDER[0]
                    html.append("<checkbox checked=\"$isChecked\" index=\"$currentPos\"/>")
                    currentPos++
                    continue
                }
            }
            
            // 处理普通文本和其他样式
            val nextSpecialPos = findNextSpecialChar(spannable, currentPos, end)
            val segmentEnd = minOf(nextSpecialPos, end)
            
            if (currentPos < segmentEnd) {
                val segmentText = spannable.substring(currentPos, segmentEnd)
                val segmentSpans = spannable.getSpans(currentPos, segmentEnd, Any::class.java)
                
                wrapTextWithSpans(segmentText, segmentSpans, html)
            }
            
            currentPos = segmentEnd
        }
        
        if (indentSpan != null) {
            html.append("</div>")
        }
    }
    
    private fun findNextSpecialChar(spannable: Spannable, start: Int, end: Int): Int {
        for (i in start until end) {
            val char = spannable[i]
            if (char == CHECKBOX_PLACEHOLDER[0] || char == CHECKBOX_CHECKED_PLACEHOLDER[0]) {
                return i
            }
        }
        return end
    }
    
    private fun wrapTextWithSpans(text: String, spans: Array<Any>, html: StringBuilder) {
        var wrappedText = text
        
        for (span in spans) {
            when (span) {
                is StyleSpan -> {
                    when (span.style) {
                        Typeface.BOLD -> wrappedText = "<b>$wrappedText</b>"
                        Typeface.ITALIC -> wrappedText = "<i>$wrappedText</i>"
                    }
                }
                is UnderlineSpan -> {
                    wrappedText = "<u>$wrappedText</u>"
                }
                is StrikethroughSpan -> {
                    wrappedText = "<strike>$wrappedText</strike>"
                }
                is ForegroundColorSpan -> {
                    val color = String.format("#%06X", 0xFFFFFF and span.foregroundColor)
                    wrappedText = "<span style=\"color:$color\">$wrappedText</span>"
                }
                is AbsoluteSizeSpan -> {
                    val size = span.size
                    wrappedText = "<span style=\"font-size:${size}px\">$wrappedText</span>"
                }
            }
        }
        
        html.append(wrappedText)
    }
}
