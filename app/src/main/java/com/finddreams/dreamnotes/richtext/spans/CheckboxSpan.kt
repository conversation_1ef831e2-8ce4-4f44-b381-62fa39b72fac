package com.finddreams.dreamnotes.richtext.spans

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.text.style.ReplacementSpan
import androidx.core.content.ContextCompat
import com.finddreams.dreamnotes.R

/**
 * 自定义Span，用于在文本中显示可点击的checkbox
 */
class CheckboxSpan(
    private val isChecked: Boolean,
    private val onCheckChanged: ((Boolean) -> Unit)? = null
) : ReplacementSpan() {
    
    companion object {
        private const val CHECKBOX_SIZE = 48f // checkbox大小
        private const val CHECKBOX_MARGIN = 16f // checkbox右边距
    }
    
    private var checkboxRect = RectF()
    
    override fun getSize(
        paint: Paint,
        text: CharSequence?,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        return (CHECKBOX_SIZE + CHECKBOX_MARGIN).toInt()
    }
    
    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val centerY = (top + bottom) / 2f
        val checkboxTop = centerY - CHECKBOX_SIZE / 2
        val checkboxBottom = centerY + CHECKBOX_SIZE / 2
        
        checkboxRect.set(
            x,
            checkboxTop,
            x + CHECKBOX_SIZE,
            checkboxBottom
        )
        
        // 绘制checkbox边框
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        paint.color = 0xFF666666.toInt()
        canvas.drawRoundRect(checkboxRect, 8f, 8f, paint)
        
        // 如果选中，绘制勾选标记
        if (isChecked) {
            paint.style = Paint.Style.FILL
            paint.color = 0xFF4CAF50.toInt()
            canvas.drawRoundRect(checkboxRect, 8f, 8f, paint)
            
            // 绘制白色勾号
            paint.color = 0xFFFFFFFF.toInt()
            paint.strokeWidth = 4f
            paint.style = Paint.Style.STROKE
            
            val checkSize = CHECKBOX_SIZE * 0.3f
            val checkCenterX = checkboxRect.centerX()
            val checkCenterY = checkboxRect.centerY()
            
            canvas.drawLine(
                checkCenterX - checkSize,
                checkCenterY,
                checkCenterX - checkSize / 3,
                checkCenterY + checkSize / 2,
                paint
            )
            canvas.drawLine(
                checkCenterX - checkSize / 3,
                checkCenterY + checkSize / 2,
                checkCenterX + checkSize,
                checkCenterY - checkSize / 2,
                paint
            )
        }
    }
    
    /**
     * 检查点击位置是否在checkbox区域内
     */
    fun isClickInCheckbox(x: Float, y: Float): Boolean {
        return checkboxRect.contains(x, y)
    }
    
    /**
     * 处理checkbox点击
     */
    fun handleClick() {
        onCheckChanged?.invoke(!isChecked)
    }
    
    /**
     * 获取checkbox的边界矩形
     */
    fun getCheckboxBounds(): RectF = checkboxRect
}
