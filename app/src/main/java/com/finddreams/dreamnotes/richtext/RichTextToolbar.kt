package com.finddreams.dreamnotes.richtext

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 富文本编辑器工具栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RichTextToolbar(
    onBoldClick: () -> Unit,
    onItalicClick: () -> Unit,
    onUnderlineClick: () -> Unit,
    onStrikethroughClick: () -> Unit,
    onColorClick: (Color) -> Unit,
    onSizeClick: (Int) -> Unit,
    onTodoClick: () -> Unit,
    onIndentIncreaseClick: () -> Unit,
    onIndentDecreaseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showColorPicker by remember { mutableStateOf(false) }
    var showSizePicker by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            // 第一行：基本格式化按钮
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                items(getBasicFormatButtons(
                    onBoldClick = onBoldClick,
                    onItalicClick = onItalicClick,
                    onUnderlineClick = onUnderlineClick,
                    onStrikethroughClick = onStrikethroughClick
                )) { button ->
                    ToolbarButton(
                        icon = button.icon,
                        contentDescription = button.description,
                        onClick = button.onClick
                    )
                }

                item {
                    Spacer(modifier = Modifier.width(8.dp))
                    Divider(
                        modifier = Modifier
                            .height(24.dp)
                            .width(1.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }

                // 颜色选择器按钮
                item {
                    ToolbarButton(
                        icon = Icons.Default.ColorLens,
                        contentDescription = "文字颜色",
                        onClick = { showColorPicker = true }
                    )
                }

                // 字体大小按钮
                item {
                    ToolbarButton(
                        icon = Icons.Default.TextFields,
                        contentDescription = "字体大小",
                        onClick = { showSizePicker = true }
                    )
                }

                item {
                    Spacer(modifier = Modifier.width(8.dp))
                    Divider(
                        modifier = Modifier
                            .height(24.dp)
                            .width(1.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }

                // 待办事项按钮
                item {
                    ToolbarButton(
                        icon = Icons.Default.CheckBox,
                        contentDescription = "待办事项",
                        onClick = onTodoClick
                    )
                }

                // 缩进按钮
                items(getIndentButtons(
                    onIndentIncreaseClick = onIndentIncreaseClick,
                    onIndentDecreaseClick = onIndentDecreaseClick
                )) { button ->
                    ToolbarButton(
                        icon = button.icon,
                        contentDescription = button.description,
                        onClick = button.onClick
                    )
                }
            }

            // 颜色选择器
            if (showColorPicker) {
                Spacer(modifier = Modifier.height(8.dp))
                ColorPicker(
                    onColorSelected = { color ->
                        onColorClick(color)
                        showColorPicker = false
                    },
                    onDismiss = { showColorPicker = false }
                )
            }

            // 字体大小选择器
            if (showSizePicker) {
                Spacer(modifier = Modifier.height(8.dp))
                SizePicker(
                    onSizeSelected = { size ->
                        onSizeClick(size)
                        showSizePicker = false
                    },
                    onDismiss = { showSizePicker = false }
                )
            }
        }
    }
}

@Composable
private fun ToolbarButton(
    icon: ImageVector,
    contentDescription: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier.size(40.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun ColorPicker(
    onColorSelected: (Color) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val colors = listOf(
        Color.Black,
        Color.Red,
        Color.Green,
        Color.Blue,
        Color.Yellow,
        Color.Magenta,
        Color.Cyan,
        Color.Gray,
        Color(0xFF8B4513), // Brown
        Color(0xFF800080), // Purple
        Color(0xFFFFA500), // Orange
        Color(0xFF008000)  // Dark Green
    )

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "选择颜色",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(colors) { color ->
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .background(
                                color = color,
                                shape = RoundedCornerShape(4.dp)
                            )
                            .clickable { onColorSelected(color) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            TextButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("取消")
            }
        }
    }
}

@Composable
private fun SizePicker(
    onSizeSelected: (Int) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val sizes = listOf(12, 14, 16, 18, 20, 24, 28, 32, 36, 48)

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "选择字体大小",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(sizes) { size ->
                    OutlinedButton(
                        onClick = { onSizeSelected(size) },
                        modifier = Modifier.height(32.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp)
                    ) {
                        Text(
                            text = "${size}px",
                            fontSize = 12.sp
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            TextButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("取消")
            }
        }
    }
}

private data class ToolbarButtonData(
    val icon: ImageVector,
    val description: String,
    val onClick: () -> Unit
)

private fun getBasicFormatButtons(
    onBoldClick: () -> Unit,
    onItalicClick: () -> Unit,
    onUnderlineClick: () -> Unit,
    onStrikethroughClick: () -> Unit
): List<ToolbarButtonData> = listOf(
    ToolbarButtonData(Icons.Default.FormatBold, "加粗", onBoldClick),
    ToolbarButtonData(Icons.Default.FormatItalic, "斜体", onItalicClick),
    ToolbarButtonData(Icons.Default.FormatUnderlined, "下划线", onUnderlineClick),
    ToolbarButtonData(Icons.Default.StrikethroughS, "删除线", onStrikethroughClick)
)

private fun getIndentButtons(
    onIndentIncreaseClick: () -> Unit,
    onIndentDecreaseClick: () -> Unit
): List<ToolbarButtonData> = listOf(
    ToolbarButtonData(Icons.Default.KeyboardArrowLeft, "减少缩进", onIndentDecreaseClick),
    ToolbarButtonData(Icons.Default.KeyboardArrowRight, "增加缩进", onIndentIncreaseClick)
)
