package com.finddreams.dreamnotes.richtext.spans

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.text.Layout
import android.text.style.LeadingMarginSpan
import kotlin.math.max
import kotlin.math.min

/**
 * 右对齐缩进Span，实现文本右对齐并支持自动换行到下一行右边的效果
 */
class RightAlignedLeadingMarginSpan(
    private val indentLevel: Int,
    private val baseIndentSize: Int = 48,
    private val minTextWidth: Int = 150 // 最小文本宽度
) : LeadingMarginSpan.LeadingMarginSpan2 {
    
    companion object {
        private var screenWidth: Int = 0
        
        fun setScreenWidth(width: Int) {
            screenWidth = width
        }
        
        private fun getScreenWidth(): Int {
            if (screenWidth == 0) {
                screenWidth = Resources.getSystem().displayMetrics.widthPixels
            }
            return screenWidth
        }
    }
    
    override fun getLeadingMargin(first: <PERSON><PERSON><PERSON>): Int {
        val screenWidth = getScreenWidth()
        val rightIndent = indentLevel * baseIndentSize
        
        // 计算左边距，使文本右对齐
        val maxAllowedIndent = screenWidth - minTextWidth
        val actualRightIndent = min(rightIndent, maxAllowedIndent)
        
        // 为右对齐文本计算左边距
        return max(0, screenWidth - actualRightIndent - getTextWidth())
    }
    
    override fun getLeadingMarginLineCount(): Int {
        // 返回应用此span的行数，-1表示所有行
        return -1
    }
    
    override fun drawLeadingMargin(
        canvas: Canvas?,
        paint: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: Layout?
    ) {
        if (indentLevel > 0 && canvas != null && paint != null) {
            val originalColor = paint.color
            val originalStrokeWidth = paint.strokeWidth
            
            val screenWidth = getScreenWidth()
            
            // 绘制右对齐指示器
            for (i in 1..indentLevel) {
                val lineX = screenWidth - (i * baseIndentSize) + 10 // 稍微向左偏移以避免被文本覆盖
                
                if (lineX > x + 20) { // 确保指示线不与文本重叠
                    // 使用蓝色系来区分右对齐缩进
                    val alpha = (255 * (1.0f - (i - 1) * 0.15f)).toInt().coerceIn(120, 255)
                    paint.color = (alpha shl 24) or 0x002196F3
                    paint.strokeWidth = 1.5f
                    
                    // 绘制点状指示线
                    val dotSize = 3f
                    val dotSpacing = 8f
                    var currentY = top.toFloat()
                    
                    while (currentY < bottom) {
                        canvas.drawCircle(lineX.toFloat(), currentY, dotSize, paint)
                        currentY += dotSpacing
                    }
                }
            }
            
            // 绘制右边界指示线
            val rightBoundary = getRightBoundary()
            paint.color = 0x4D2196F3 // 半透明蓝色
            paint.strokeWidth = 2f
            canvas.drawLine(
                rightBoundary.toFloat(), 
                top.toFloat(), 
                rightBoundary.toFloat(), 
                bottom.toFloat(), 
                paint
            )
            
            // 恢复原始画笔设置
            paint.color = originalColor
            paint.strokeWidth = originalStrokeWidth
        }
    }
    
    /**
     * 获取文本宽度（估算）
     */
    private fun getTextWidth(): Int {
        // 这里可以根据实际文本内容计算，暂时使用固定值
        return minTextWidth
    }
    
    /**
     * 获取右边界位置
     */
    private fun getRightBoundary(): Int {
        val screenWidth = getScreenWidth()
        val rightIndent = indentLevel * baseIndentSize
        val maxAllowedIndent = screenWidth - minTextWidth
        val actualRightIndent = min(rightIndent, maxAllowedIndent)
        return screenWidth - actualRightIndent
    }
    
    /**
     * 获取缩进级别
     */
    fun getIndentLevel(): Int = indentLevel
    
    /**
     * 获取可用的文本宽度
     */
    fun getAvailableTextWidth(): Int {
        val screenWidth = getScreenWidth()
        val rightIndent = indentLevel * baseIndentSize
        val maxAllowedIndent = screenWidth - minTextWidth
        val actualRightIndent = min(rightIndent, maxAllowedIndent)
        return min(actualRightIndent, minTextWidth * 2) // 限制最大文本宽度
    }
    
    /**
     * 创建新的右对齐缩进级别
     */
    fun withIndentLevel(newLevel: Int): RightAlignedLeadingMarginSpan {
        return RightAlignedLeadingMarginSpan(newLevel, baseIndentSize, minTextWidth)
    }
    
    /**
     * 检查是否达到最大右对齐缩进限制
     */
    fun isAtMaxRightIndent(): Boolean {
        val screenWidth = getScreenWidth()
        val rightIndent = indentLevel * baseIndentSize
        val maxAllowedIndent = screenWidth - minTextWidth
        return rightIndent >= maxAllowedIndent
    }
    
    /**
     * 获取建议的最大右对齐缩进级别
     */
    fun getMaxRecommendedLevel(): Int {
        val screenWidth = getScreenWidth()
        val maxAllowedIndent = screenWidth - minTextWidth
        return maxAllowedIndent / baseIndentSize
    }
}
