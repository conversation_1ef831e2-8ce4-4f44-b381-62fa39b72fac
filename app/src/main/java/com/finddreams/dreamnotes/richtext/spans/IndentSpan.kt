package com.finddreams.dreamnotes.richtext.spans

import android.graphics.Canvas
import android.graphics.Paint
import android.text.style.LeadingMarginSpan

/**
 * 自定义缩进Span，用于实现文本的左右缩进功能
 */
class IndentSpan(
    private val indentLevel: Int,
    private val indentSize: Int = 60 // 每级缩进的像素大小
) : LeadingMarginSpan {
    
    override fun getLeadingMargin(first: Boolean): Int {
        return indentLevel * indentSize
    }
    
    override fun drawLeadingMargin(
        canvas: Canvas?,
        paint: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: android.text.Layout?
    ) {
        // 可以在这里绘制缩进指示器，比如竖线或点
        if (indentLevel > 0 && canvas != null && paint != null) {
            val originalColor = paint.color
            paint.color = 0xFFE0E0E0.toInt()
            paint.strokeWidth = 2f
            
            // 绘制缩进级别指示线
            for (i in 1..indentLevel) {
                val lineX = x + (i * indentSize) - (indentSize / 2)
                canvas.drawLine(
                    lineX.toFloat(),
                    top.toFloat(),
                    lineX.toFloat(),
                    bottom.toFloat(),
                    paint
                )
            }
            
            paint.color = originalColor
        }
    }
    
    /**
     * 获取缩进级别
     */
    fun getIndentLevel(): Int = indentLevel
    
    /**
     * 创建新的缩进级别
     */
    fun withIndentLevel(newLevel: Int): IndentSpan {
        return IndentSpan(newLevel, indentSize)
    }
}
