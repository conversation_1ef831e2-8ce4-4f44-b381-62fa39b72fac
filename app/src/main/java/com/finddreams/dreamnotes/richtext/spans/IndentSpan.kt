package com.finddreams.dreamnotes.richtext.spans

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.text.style.LeadingMarginSpan
import kotlin.math.min

/**
 * 自定义缩进Span，用于实现文本的左右缩进功能
 * 支持自动换行，确保文本不会被挤压到屏幕外
 */
class IndentSpan(
    private val indentLevel: Int,
    private val baseIndentSize: Int = 48, // 基础缩进大小（减小以留更多空间给文本）
    private val maxIndentRatio: Float = 0.6f // 最大缩进占屏幕宽度的比例
) : LeadingMarginSpan {

    companion object {
        private var screenWidth: Int = 0

        fun setScreenWidth(width: Int) {
            screenWidth = width
        }

        private fun getScreenWidth(): Int {
            if (screenWidth == 0) {
                screenWidth = Resources.getSystem().displayMetrics.widthPixels
            }
            return screenWidth
        }
    }

    override fun getLeadingMargin(first: Boolean): Int {
        val maxAllowedIndent = (getScreenWidth() * maxIndentRatio).toInt()
        val calculatedIndent = indentLevel * baseIndentSize

        // 确保缩进不超过屏幕宽度的60%，为文本内容保留足够空间
        return min(calculatedIndent, maxAllowedIndent)
    }

    override fun drawLeadingMargin(
        canvas: Canvas?,
        paint: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: android.text.Layout?
    ) {
        // 绘制缩进指示器，使用更精细的视觉效果
        if (indentLevel > 0 && canvas != null && paint != null) {
            val originalColor = paint.color
            val originalStrokeWidth = paint.strokeWidth

            // 计算实际的缩进边距
            val actualIndent = getLeadingMargin(first)
            val stepSize = if (indentLevel > 0) actualIndent / indentLevel else baseIndentSize

            // 绘制缩进级别指示
            for (i in 1..indentLevel) {
                val lineX = x + (i * stepSize) - (stepSize / 2)

                // 只在实际缩进范围内绘制指示线
                if (lineX < x + actualIndent) {
                    // 使用渐变透明度，越深层级越淡
                    val alpha = (255 * (1.0f - (i - 1) * 0.2f)).toInt().coerceIn(80, 255)
                    paint.color = (alpha shl 24) or 0x00E0E0E0
                    paint.strokeWidth = 1.5f

                    // 绘制虚线效果
                    val dashLength = 8f
                    val gapLength = 4f
                    var currentY = top.toFloat()

                    while (currentY < bottom) {
                        val endY = minOf(currentY + dashLength, bottom.toFloat())
                        canvas.drawLine(lineX, currentY, lineX, endY, paint)
                        currentY += dashLength + gapLength
                    }
                }
            }

            // 恢复原始画笔设置
            paint.color = originalColor
            paint.strokeWidth = originalStrokeWidth
        }
    }

    /**
     * 获取缩进级别
     */
    fun getIndentLevel(): Int = indentLevel

    /**
     * 获取实际的缩进大小（像素）
     */
    fun getActualIndentSize(): Int = getLeadingMargin(true)

    /**
     * 获取可用的文本宽度
     */
    fun getAvailableTextWidth(): Int {
        val screenWidth = getScreenWidth()
        val indentSize = getLeadingMargin(true)
        return screenWidth - indentSize - 32 // 减去padding
    }

    /**
     * 创建新的缩进级别
     */
    fun withIndentLevel(newLevel: Int): IndentSpan {
        return IndentSpan(newLevel, baseIndentSize, maxIndentRatio)
    }

    /**
     * 检查是否达到最大缩进限制
     */
    fun isAtMaxIndent(): Boolean {
        val maxAllowedIndent = (getScreenWidth() * maxIndentRatio).toInt()
        val calculatedIndent = indentLevel * baseIndentSize
        return calculatedIndent >= maxAllowedIndent
    }

    /**
     * 获取建议的最大缩进级别
     */
    fun getMaxRecommendedLevel(): Int {
        val maxAllowedIndent = (getScreenWidth() * maxIndentRatio).toInt()
        return maxAllowedIndent / baseIndentSize
    }
}
