package com.finddreams.dreamnotes.richtext

import android.content.Context
import android.graphics.Typeface
import android.text.*
import android.text.style.*
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatEditText
import com.finddreams.dreamnotes.richtext.spans.CheckboxSpan
import com.finddreams.dreamnotes.richtext.spans.IndentSpan
import com.finddreams.dreamnotes.richtext.spans.RightAlignedLeadingMarginSpan

/**
 * 支持富文本编辑的自定义EditText
 */
class RichTextEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle
) : AppCompatEditText(context, attrs, defStyleAttr) {

    private var onCheckboxClickListener: ((Int, Boolean) -> Unit)? = null

    init {
        // 设置基本属性
        inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_FLAG_MULTI_LINE
        isSingleLine = false
        maxLines = Int.MAX_VALUE

        // 启用自动换行
        setHorizontallyScrolling(false)

        // 添加文本变化监听器
        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                // 可以在这里处理文本变化后的逻辑
            }
        })
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 更新IndentSpan的屏幕宽度信息
        IndentSpan.setScreenWidth(w)
        // 更新右对齐缩进Span的屏幕宽度信息
        RightAlignedLeadingMarginSpan.setScreenWidth(w)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val x = event.x - totalPaddingLeft
            val y = event.y - totalPaddingTop

            val layout = layout ?: return super.onTouchEvent(event)
            val line = layout.getLineForVertical(y.toInt())
            val offset = layout.getOffsetForHorizontal(line, x)

            // 检查是否点击了checkbox
            val spannable = text as? Spannable
            if (spannable != null) {
                val checkboxSpans = spannable.getSpans(offset, offset + 1, CheckboxSpan::class.java)
                for (span in checkboxSpans) {
                    if (span.isClickInCheckbox(x, y)) {
                        span.handleClick()
                        return true
                    }
                }
            }
        }

        return super.onTouchEvent(event)
    }

    /**
     * 设置checkbox点击监听器
     */
    fun setOnCheckboxClickListener(listener: (Int, Boolean) -> Unit) {
        onCheckboxClickListener = listener
    }

    /**
     * 应用加粗样式
     */
    fun applyBold() {
        applyStyle { spannable, start, end ->
            val existingSpans = spannable.getSpans(start, end, StyleSpan::class.java)
            val hasBold = existingSpans.any { it.style == Typeface.BOLD }

            if (hasBold) {
                // 移除加粗
                existingSpans.filter { it.style == Typeface.BOLD }.forEach {
                    spannable.removeSpan(it)
                }
            } else {
                // 添加加粗
                spannable.setSpan(
                    StyleSpan(Typeface.BOLD),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    /**
     * 应用斜体样式
     */
    fun applyItalic() {
        applyStyle { spannable, start, end ->
            val existingSpans = spannable.getSpans(start, end, StyleSpan::class.java)
            val hasItalic = existingSpans.any { it.style == Typeface.ITALIC }

            if (hasItalic) {
                existingSpans.filter { it.style == Typeface.ITALIC }.forEach {
                    spannable.removeSpan(it)
                }
            } else {
                spannable.setSpan(
                    StyleSpan(Typeface.ITALIC),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    /**
     * 应用下划线样式
     */
    fun applyUnderline() {
        applyStyle { spannable, start, end ->
            val existingSpans = spannable.getSpans(start, end, UnderlineSpan::class.java)

            if (existingSpans.isNotEmpty()) {
                existingSpans.forEach { spannable.removeSpan(it) }
            } else {
                spannable.setSpan(
                    UnderlineSpan(),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    /**
     * 应用删除线样式
     */
    fun applyStrikethrough() {
        applyStyle { spannable, start, end ->
            val existingSpans = spannable.getSpans(start, end, StrikethroughSpan::class.java)

            if (existingSpans.isNotEmpty()) {
                existingSpans.forEach { spannable.removeSpan(it) }
            } else {
                spannable.setSpan(
                    StrikethroughSpan(),
                    start, end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    /**
     * 设置文字颜色
     */
    fun applyTextColor(color: Int) {
        applyStyle { spannable, start, end ->
            // 移除现有的颜色span
            val existingSpans = spannable.getSpans(start, end, ForegroundColorSpan::class.java)
            existingSpans.forEach { spannable.removeSpan(it) }

            // 添加新的颜色span
            spannable.setSpan(
                ForegroundColorSpan(color),
                start, end,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }

    /**
     * 设置字体大小
     */
    fun applyTextSize(sizePx: Int) {
        applyStyle { spannable, start, end ->
            // 移除现有的大小span
            val existingSpans = spannable.getSpans(start, end, AbsoluteSizeSpan::class.java)
            existingSpans.forEach { spannable.removeSpan(it) }

            // 添加新的大小span
            spannable.setSpan(
                AbsoluteSizeSpan(sizePx, false),
                start, end,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }

    /**
     * 插入待办事项
     */
    fun insertTodoItem() {
        val start = selectionStart
        val spannable = text as? SpannableStringBuilder ?: return

        // 插入checkbox占位符
        val checkboxText = "☐ "
        spannable.insert(start, checkboxText)

        // 添加checkbox span
        val checkboxSpan = CheckboxSpan(false) { isChecked ->
            onCheckboxClickListener?.invoke(start, isChecked)
            toggleStrikethroughForLine(start, isChecked)
        }

        spannable.setSpan(
            checkboxSpan,
            start,
            start + 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 移动光标到checkbox后面
        setSelection(start + checkboxText.length)
    }

    /**
     * 增加缩进
     */
    fun increaseIndent() {
        val currentLine = getCurrentLineRange()
        if (currentLine != null) {
            val spannable = text as? SpannableStringBuilder ?: return
            val existingIndent = spannable.getSpans(
                currentLine.first,
                currentLine.second,
                IndentSpan::class.java
            ).firstOrNull()

            val currentLevel = existingIndent?.getIndentLevel() ?: 0
            val newLevel = currentLevel + 1

            // 创建临时IndentSpan来检查最大缩进限制
            val tempIndent = IndentSpan(newLevel)
            val maxLevel = tempIndent.getMaxRecommendedLevel()

            // 检查是否超过最大推荐缩进级别
            if (newLevel > maxLevel) {
                // 可以在这里显示提示信息
                return
            }

            // 移除旧的缩进span
            existingIndent?.let { spannable.removeSpan(it) }

            // 添加新的缩进span
            spannable.setSpan(
                IndentSpan(newLevel),
                currentLine.first,
                currentLine.second,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 强制重新布局以应用新的缩进
            requestLayout()
        }
    }

    /**
     * 减少缩进
     */
    fun decreaseIndent() {
        val currentLine = getCurrentLineRange()
        if (currentLine != null) {
            val spannable = text as? SpannableStringBuilder ?: return
            val existingIndent = spannable.getSpans(
                currentLine.first,
                currentLine.second,
                IndentSpan::class.java
            ).firstOrNull()

            if (existingIndent != null) {
                val newLevel = maxOf(0, existingIndent.getIndentLevel() - 1)
                spannable.removeSpan(existingIndent)

                if (newLevel > 0) {
                    spannable.setSpan(
                        IndentSpan(newLevel),
                        currentLine.first,
                        currentLine.second,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                // 强制重新布局以应用新的缩进
                requestLayout()
            }
        }
    }

    /**
     * 增加右缩进（右对齐缩进）
     */
    fun increaseRightIndent() {
        val currentLine = getCurrentLineRange()
        if (currentLine != null) {
            val spannable = text as? SpannableStringBuilder ?: return

            // 移除现有的左缩进（如果有）
            val existingLeftIndent = spannable.getSpans(
                currentLine.first,
                currentLine.second,
                IndentSpan::class.java
            ).firstOrNull()
            existingLeftIndent?.let { spannable.removeSpan(it) }

            // 检查现有的右缩进
            val existingRightIndent = spannable.getSpans(
                currentLine.first,
                currentLine.second,
                RightAlignedLeadingMarginSpan::class.java
            ).firstOrNull()

            val currentLevel = existingRightIndent?.getIndentLevel() ?: 0
            val newLevel = currentLevel + 1

            // 创建临时右缩进Span来检查最大缩进限制
            val tempIndent = RightAlignedLeadingMarginSpan(newLevel)
            val maxLevel = tempIndent.getMaxRecommendedLevel()

            // 检查是否超过最大推荐缩进级别
            if (newLevel > maxLevel) {
                return
            }

            // 移除旧的右缩进span
            existingRightIndent?.let { spannable.removeSpan(it) }

            // 添加新的右缩进span
            spannable.setSpan(
                RightAlignedLeadingMarginSpan(newLevel),
                currentLine.first,
                currentLine.second,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 强制重新布局以应用新的缩进
            requestLayout()
        }
    }

    /**
     * 减少右缩进
     */
    fun decreaseRightIndent() {
        val currentLine = getCurrentLineRange()
        if (currentLine != null) {
            val spannable = text as? SpannableStringBuilder ?: return
            val existingRightIndent = spannable.getSpans(
                currentLine.first,
                currentLine.second,
                RightAlignedLeadingMarginSpan::class.java
            ).firstOrNull()

            if (existingRightIndent != null) {
                val newLevel = maxOf(0, existingRightIndent.getIndentLevel() - 1)
                spannable.removeSpan(existingRightIndent)

                if (newLevel > 0) {
                    spannable.setSpan(
                        RightAlignedLeadingMarginSpan(newLevel),
                        currentLine.first,
                        currentLine.second,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                // 强制重新布局以应用新的缩进
                requestLayout()
            }
        }
    }

    private fun applyStyle(action: (SpannableStringBuilder, Int, Int) -> Unit) {
        val spannable = text as? SpannableStringBuilder ?: return
        val start = selectionStart
        val end = selectionEnd

        if (start != end) {
            // 有选中文本，应用到选中范围
            action(spannable, start, end)
        } else {
            // 没有选中文本，应用到当前输入
            // 这里可以设置一个标记，在下次输入时应用样式
        }
    }

    private fun getCurrentLineRange(): Pair<Int, Int>? {
        val text = text?.toString() ?: return null
        val cursor = selectionStart

        val lineStart = text.lastIndexOf('\n', cursor - 1) + 1
        val lineEnd = text.indexOf('\n', cursor).let { if (it == -1) text.length else it }

        return Pair(lineStart, lineEnd)
    }

    /**
     * 为指定行切换删除线效果（公开方法）
     */
    fun toggleStrikethroughForLine(checkboxPosition: Int, isChecked: Boolean) {
        val spannable = text as? SpannableStringBuilder ?: return
        val lineRange = getCurrentLineRange() ?: return

        // 找到checkbox后面的文本范围
        val textStart = checkboxPosition + 2 // 跳过checkbox和空格
        val textEnd = lineRange.second

        if (textStart < textEnd) {
            val existingSpans = spannable.getSpans(textStart, textEnd, StrikethroughSpan::class.java)

            // 移除现有的删除线
            existingSpans.forEach { spannable.removeSpan(it) }

            // 如果选中，添加删除线
            if (isChecked) {
                spannable.setSpan(
                    StrikethroughSpan(),
                    textStart,
                    textEnd,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }
}
