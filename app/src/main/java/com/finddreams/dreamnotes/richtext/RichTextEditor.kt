package com.finddreams.dreamnotes.richtext

import android.graphics.Color
import android.text.TextWatcher
import android.view.View
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.finddreams.dreamnotes.richtext.utils.HtmlSpanConverter

/**
 * 富文本编辑器主组件
 */
@Composable
fun RichTextEditor(
    initialHtml: String = "",
    onContentChanged: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var editText by remember { mutableStateOf<RichTextEditText?>(null) }
    val htmlConverter = remember { HtmlSpanConverter() }
    var isInitialized by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // 工具栏
        RichTextToolbar(
            onBoldClick = { editText?.applyBold() },
            onItalicClick = { editText?.applyItalic() },
            onUnderlineClick = { editText?.applyUnderline() },
            onStrikethroughClick = { editText?.applyStrikethrough() },
            onColorClick = { color ->
                editText?.applyTextColor(color.toArgb())
            },
            onSizeClick = { size ->
                editText?.applyTextSize(size)
            },
            onTodoClick = { editText?.insertTodoItem() },
            onIndentIncreaseClick = { editText?.increaseIndent() },
            onIndentDecreaseClick = { editText?.decreaseIndent() },
            onRightIndentIncreaseClick = { editText?.increaseRightIndent() },
            onRightIndentDecreaseClick = { editText?.decreaseRightIndent() },
            modifier = Modifier.padding(8.dp)
        )

        // 编辑器
        AndroidView<RichTextEditText>(
            factory = { ctx ->
                RichTextEditText(ctx).apply {
                    editText = this
                    setPadding(16, 16, 16, 16)

                    // 初始化内容（只在创建时执行一次）
                    if (!isInitialized && initialHtml.isNotEmpty()) {
                        val spannable = htmlConverter.fromHtml(initialHtml) { index, isChecked ->
                            toggleStrikethroughForLine(index, isChecked)
                        }
                        setText(spannable)
                        isInitialized = true
                    }

                    // 设置checkbox点击监听器
                    setOnCheckboxClickListener { index, isChecked ->
                        toggleStrikethroughForLine(index, isChecked)

                        // 通知内容变化
                        val currentText = text
                        if (currentText != null) {
                            val html = htmlConverter.toHtml(currentText)
                            onContentChanged(html)
                        }
                    }

                    // 设置文本变化监听器
                    addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                        override fun afterTextChanged(s: android.text.Editable?) {
                            // 只有用户输入才触发回调，避免程序设置文本时触发
                            if (isInitialized) {
                                s?.let {
                                    val html = htmlConverter.toHtml(it)
                                    onContentChanged(html)
                                }
                            }
                        }
                    })
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(8.dp)
        )
    }
}

/**
 * 富文本编辑器的简化版本，用于预览
 */
@Composable
fun RichTextViewer(
    html: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val htmlConverter = remember { HtmlSpanConverter() }

    AndroidView<RichTextEditText>(
        factory = { ctx ->
            RichTextEditText(ctx).apply {
                isEnabled = false
                isFocusable = false
                setPadding(16, 16, 16, 16)

                val spannable = htmlConverter.fromHtml(html)
                setText(spannable)
            }
        },
        update = { editText ->
            val spannable = htmlConverter.fromHtml(html)
            editText.setText(spannable)
        },
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp)
    )
}

/**
 * 富文本编辑器的工具函数
 */
object RichTextUtils {

    /**
     * 创建一个简单的HTML模板
     */
    fun createSimpleHtml(text: String): String {
        return "<html><body>$text</body></html>"
    }

    /**
     * 从HTML中提取纯文本
     */
    fun extractPlainText(html: String): String {
        return html
            .replace(Regex("<[^>]*>"), "")
            .replace("&nbsp;", " ")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&amp;", "&")
            .trim()
    }

    /**
     * 检查HTML是否包含待办事项
     */
    fun containsTodoItems(html: String): Boolean {
        return html.contains("<checkbox")
    }

    /**
     * 统计HTML中的待办事项数量
     */
    fun countTodoItems(html: String): Pair<Int, Int> {
        val totalTodos = Regex("<checkbox[^>]*>").findAll(html).count()
        val completedTodos = Regex("<checkbox[^>]*checked=\"true\"[^>]*>").findAll(html).count()
        return Pair(completedTodos, totalTodos)
    }

    /**
     * 创建一个包含待办事项的HTML示例
     */
    fun createTodoExample(): String {
        return """
            <html>
            <body>
                <div>我的待办事项：</div>
                <br>
                <checkbox checked="false" index="0"/>完成项目文档
                <br>
                <checkbox checked="true" index="1"/>开会讨论需求
                <br>
                <div indent="1">
                    <checkbox checked="false" index="2"/>准备会议材料
                </div>
                <br>
                <b>重要提醒：</b>
                <br>
                <span style="color:#ff0000">明天截止日期</span>
            </body>
            </html>
        """.trimIndent()
    }

    /**
     * 创建一个包含右缩进示例的HTML
     */
    fun createRightIndentExample(): String {
        return """
            <html>
            <body>
                <div>富文本编辑器功能演示</div>
                <br>
                <div>左对齐文本：这是普通的左对齐文本内容。</div>
                <br>
                <div rightindent="1">右缩进1级：这段文字向右缩进一级，当文字过长时会自动换行到下一行的右边位置。</div>
                <br>
                <div rightindent="2">右缩进2级：这段文字向右缩进两级，展示更深层次的右对齐缩进效果。文字会自动换行并保持右对齐。</div>
                <br>
                <div indent="1">左缩进1级：这是传统的左缩进文本。</div>
                <br>
                <div indent="1" rightindent="1">混合缩进：这段文字同时具有左缩进和右缩进，创造出居中的效果。</div>
                <br>
                <div rightindent="1">
                    <checkbox checked="false" index="0"/>右缩进的待办事项：测试右缩进与待办事项的结合效果
                </div>
                <br>
                <div rightindent="2">
                    <b>右缩进的加粗文本</b>：测试右缩进与其他格式的组合效果
                </div>
            </body>
            </html>
        """.trimIndent()
    }
}
