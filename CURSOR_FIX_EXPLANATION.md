# 光标跳转问题修复说明

## 🐛 问题描述

在使用`RichTextEditText`时，当用户在文本末尾输入文字时，光标会自动跳转到文本开头，导致用户体验很差。

## 🔍 问题原因分析

经过分析，发现光标跳转问题主要由以下几个原因造成：

### 1. LaunchedEffect重复触发
```kotlin
// 问题代码
LaunchedEffect(initialHtml) {
    editText?.let { et ->
        if (initialHtml.isNotEmpty()) {
            val spannable = htmlConverter.fromHtml(initialHtml)
            et.setText(spannable) // 每次initialHtml变化都会重新设置文本
        }
    }
}
```

**问题**：每当`initialHtml`发生变化时，`LaunchedEffect`都会重新执行，调用`setText()`方法，这会重置光标位置。

### 2. AndroidView的update方法
```kotlin
// 问题代码
AndroidView(
    update = { editText ->
        val spannable = htmlConverter.fromHtml(html)
        editText.setText(spannable) // 每次重组都会重新设置文本
    }
)
```

**问题**：在Compose重组时，`update`方法会被调用，重新设置文本内容，导致光标位置丢失。

### 3. TextWatcher循环触发
```kotlin
// 问题代码
addTextChangedListener(object : TextWatcher {
    override fun afterTextChanged(s: Editable?) {
        s?.let {
            val html = htmlConverter.toHtml(it)
            onContentChanged(html) // 可能触发重组，导致重新设置文本
        }
    }
})
```

**问题**：文本变化监听器可能在程序设置文本时也被触发，造成不必要的回调。

## ✅ 解决方案

### 1. 移除LaunchedEffect，改用factory初始化
```kotlin
// 修复后的代码
@Composable
fun RichTextEditor(
    initialHtml: String = "",
    onContentChanged: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    var isInitialized by remember { mutableStateOf(false) }
    
    AndroidView<RichTextEditText>(
        factory = { ctx ->
            RichTextEditText(ctx).apply {
                // 只在创建时初始化一次
                if (!isInitialized && initialHtml.isNotEmpty()) {
                    val spannable = htmlConverter.fromHtml(initialHtml)
                    setText(spannable)
                    isInitialized = true
                }
            }
        }
    )
}
```

**优点**：
- 只在EditText创建时初始化一次
- 避免重复设置文本内容
- 保持光标位置不变

### 2. 优化TextWatcher逻辑
```kotlin
// 修复后的代码
addTextChangedListener(object : TextWatcher {
    override fun afterTextChanged(s: Editable?) {
        // 只有初始化完成后才触发回调
        if (isInitialized) {
            s?.let {
                val html = htmlConverter.toHtml(it)
                onContentChanged(html)
            }
        }
    }
})
```

**优点**：
- 避免初始化时触发不必要的回调
- 只响应用户的真实输入
- 减少重组次数

### 3. 移除AndroidView的update方法
```kotlin
// 修复后的代码
AndroidView<RichTextEditText>(
    factory = { ctx -> /* 初始化逻辑 */ },
    // 不再使用update方法，避免重复设置文本
    modifier = modifier
)
```

**优点**：
- 避免Compose重组时重新设置文本
- 保持EditText的内部状态稳定
- 光标位置不会被意外重置

## 🎯 修复效果

### 修复前
- ❌ 在文本末尾输入时光标跳到开头
- ❌ 用户体验差，无法正常编辑
- ❌ 频繁的文本重设导致性能问题

### 修复后
- ✅ 光标位置保持稳定
- ✅ 用户可以正常在任意位置输入
- ✅ 性能优化，减少不必要的重组
- ✅ 保持所有富文本功能正常工作

## 🧪 测试验证

### 测试步骤
1. 启动应用，打开富文本编辑器
2. 在编辑器中输入一些文本
3. 将光标移动到文本末尾
4. 继续输入新的文字
5. 验证光标是否保持在输入位置

### 预期结果
- 光标应该始终保持在用户输入的位置
- 不会跳转到文本开头或其他位置
- 所有富文本功能（加粗、斜体等）正常工作
- 待办事项功能正常工作

## 📝 代码变更总结

### 主要变更文件
- `app/src/main/java/com/finddreams/dreamnotes/richtext/RichTextEditor.kt`

### 关键变更点
1. **移除LaunchedEffect**：避免重复初始化
2. **添加isInitialized标志**：控制初始化时机
3. **优化TextWatcher**：只响应用户输入
4. **简化AndroidView**：移除update方法

### 兼容性
- ✅ 保持所有现有功能
- ✅ 不影响API接口
- ✅ 向后兼容
- ✅ 性能提升

## 🚀 使用建议

### 最佳实践
1. **避免频繁更改initialHtml**：只在真正需要重新初始化时更改
2. **合理使用onContentChanged**：避免在回调中直接修改initialHtml
3. **性能监控**：在大量文本时注意性能表现

### 注意事项
- 如果需要程序化设置文本内容，建议直接操作EditText实例
- 避免在TextWatcher回调中进行耗时操作
- 保持HTML转换的效率，避免复杂的HTML结构

这个修复确保了富文本编辑器的光标行为符合用户期望，提供了流畅的编辑体验。
