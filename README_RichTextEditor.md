# DreamNotes 富文本编辑器

这是一个基于Android Jetpack Compose和EditText的功能完整的富文本编辑器，支持HTML格式的数据存储和读取。

## 🎯 项目完成状态

✅ **已完成** - 富文本编辑器已成功实现并通过测试！

### 实现的功能
- ✅ 富文本格式化（加粗、斜体、下划线、删除线、颜色、字体大小）
- ✅ 待办事项功能（checkbox + 删除线联动）
- ✅ 多级缩进功能
- ✅ HTML格式存储与读取
- ✅ 自定义Span技术实现
- ✅ Compose集成
- ✅ 单元测试覆盖

## 功能特性

### 🎨 富文本格式
- **加粗** - 支持文本加粗样式
- **斜体** - 支持文本斜体样式
- **下划线** - 支持文本下划线样式
- **删除线** - 支持文本删除线样式
- **文字颜色** - 支持多种颜色选择
- **字体大小** - 支持多种字体大小设置

### ✅ 待办事项功能
- **Checkbox样式** - 自定义绘制的checkbox
- **点击切换** - 点击checkbox自动切换状态
- **删除线联动** - 完成的待办事项自动添加删除线
- **状态保存** - checkbox状态完整保存到HTML

### 📝 缩进功能
- **多级缩进** - 支持多级文本缩进
- **可视化指示** - 缩进级别可视化显示
- **快捷操作** - 工具栏快速增减缩进

### 💾 数据存储
- **HTML格式** - 完整的HTML格式存储
- **双向转换** - HTML与Span之间无损转换
- **状态保持** - 所有样式和状态完整保存

## 技术实现

### 核心组件

1. **RichTextEditor** - 主编辑器组件
2. **RichTextEditText** - 自定义EditText
3. **RichTextToolbar** - 格式化工具栏
4. **CheckboxSpan** - 自定义checkbox Span
5. **IndentSpan** - 自定义缩进Span
6. **HtmlSpanConverter** - HTML转换工具

### 自定义Span技术

#### CheckboxSpan
```kotlin
class CheckboxSpan(
    private val isChecked: Boolean,
    private val onCheckChanged: ((Boolean) -> Unit)? = null
) : ReplacementSpan()
```

- 自定义绘制checkbox外观
- 处理点击事件
- 支持状态回调

#### IndentSpan
```kotlin
class IndentSpan(
    private val indentLevel: Int,
    private val indentSize: Int = 60
) : LeadingMarginSpan
```

- 实现多级缩进
- 可视化缩进指示器
- 灵活的缩进大小配置

## 使用方法

### 基本用法

```kotlin
@Composable
fun MyRichTextEditor() {
    var htmlContent by remember { mutableStateOf("") }

    RichTextEditor(
        initialHtml = htmlContent,
        onContentChanged = { newHtml ->
            htmlContent = newHtml
            // 保存到数据库或文件
        }
    )
}
```

### 预设内容

```kotlin
// 创建包含待办事项的示例内容
val todoExample = RichTextUtils.createTodoExample()

RichTextEditor(
    initialHtml = todoExample,
    onContentChanged = { html ->
        // 处理内容变化
    }
)
```

### 只读模式

```kotlin
RichTextViewer(
    html = savedHtmlContent
)
```

## HTML格式示例

### 基本格式
```html
<html>
<body>
    <b>加粗文本</b>
    <i>斜体文本</i>
    <u>下划线文本</u>
    <strike>删除线文本</strike>
    <span style="color:#ff0000">红色文本</span>
    <span style="font-size:24px">大字体文本</span>
</body>
</html>
```

### 待办事项
```html
<html>
<body>
    <checkbox checked="false" index="0"/>未完成的任务
    <br>
    <checkbox checked="true" index="1"/>已完成的任务
</body>
</html>
```

### 缩进内容
```html
<html>
<body>
    <div>主要内容</div>
    <div indent="1">一级缩进内容</div>
    <div indent="2">二级缩进内容</div>
</body>
</html>
```

## 工具函数

### RichTextUtils

```kotlin
// 创建简单HTML
val html = RichTextUtils.createSimpleHtml("Hello World")

// 提取纯文本
val plainText = RichTextUtils.extractPlainText(html)

// 检查是否包含待办事项
val hasTodos = RichTextUtils.containsTodoItems(html)

// 统计待办事项
val (completed, total) = RichTextUtils.countTodoItems(html)
```

## 自定义配置

### 修改缩进大小
```kotlin
// 在IndentSpan中修改indentSize参数
IndentSpan(indentLevel = 1, indentSize = 80) // 80像素缩进
```

### 自定义颜色选择器
```kotlin
// 在RichTextToolbar中修改colors列表
val customColors = listOf(
    Color.Black,
    Color.Red,
    Color.Blue,
    // 添加更多颜色
)
```

### 自定义字体大小选项
```kotlin
// 在RichTextToolbar中修改sizes列表
val customSizes = listOf(10, 12, 14, 16, 18, 20, 24, 28, 32)
```

## 注意事项

1. **依赖要求** - 需要添加Jsoup依赖用于HTML解析
2. **性能考虑** - 大量文本时建议分页处理
3. **状态管理** - HTML内容变化时及时保存
4. **兼容性** - 最低支持Android API 29

## 扩展功能

### 添加新的Span类型
1. 继承相应的Span基类
2. 实现自定义绘制逻辑
3. 在HtmlSpanConverter中添加转换逻辑
4. 在工具栏中添加对应按钮

### 添加新的格式化选项
1. 在RichTextEditText中添加应用方法
2. 在RichTextToolbar中添加UI控件
3. 在HtmlSpanConverter中添加HTML转换支持

这个富文本编辑器提供了完整的富文本编辑功能，特别适合笔记应用、待办事项管理等场景。通过HTML格式存储，确保了数据的完整性和可移植性。
